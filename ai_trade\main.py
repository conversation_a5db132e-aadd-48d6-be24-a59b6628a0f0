from apscheduler.schedulers.blocking import BlockingScheduler
from pprint import pprint
from binance_api import BinanceAPI, get_trading_status, get_open_futures_orders, cancel_order, close_order
from binance.enums import SIDE_BUY, SIDE_SELL, ORDER_TYPE_LIMIT, TIME_IN_FORCE_GTC
import json
from langchain_agent import LangchainAgent
from datetime import datetime
from decimal import Decimal, ROUND_CEILING
import time

file_path = 'order_record.json'

def load_order_record():
    binance = BinanceAPI(symbol='ETHUSDT')
    orders = binance.get_trading_status()
    main_orders = binance.get_open_futures_orders()
    """從 JSON 檔案中讀取訂單記錄"""
    if len(orders) == 0:
        print("訂單記錄檔案不存在或為空，將創建新的訂單記錄。")
        return None,None
    elif len(orders) == 1 or (len(orders) == 2 and len(main_orders) == 0):
        for order in orders:
            binance.cancel_order(order['orderId'])
        return None,None
    elif len(orders) == 2 and len(main_orders) != 0:
        main_order = main_orders[0]
        stop_loss_order = None
        take_profit_order = None
        for order in orders:
            if order.get("type") == "STOP_MARKET":
                stop_loss_order = order
            elif order.get("type") == "TAKE_PROFIT_MARKET":
                take_profit_order = order
        return {'main_order':main_order,'stop_loss_order':stop_loss_order,'take_profit_order':take_profit_order},True

    elif len(main_orders) != 0 and len(orders) == 3:
        main_order = None
        stop_loss_order = None
        take_profit_order = None
        for order in orders:
            binance.cancel_order(order['orderId'])
        return None,None

    
def round_up(value, decimals=3):
    return Decimal(value).quantize(Decimal(f'1.{"0" * decimals}'), rounding=ROUND_CEILING)

def check_trading_status():
    print("查詢帳戶活躍訂單...")
    order_record, main_order_processing = load_order_record()
    ask_langchain_agent(order_record, main_order_processing)

def ask_langchain_agent(order_record, main_order_processing):
    agent = LangchainAgent()
    if order_record == None:
        response = agent.get_prompt_for_new_order()
    elif main_order_processing:
        response = agent.get_prompt_for_stop_loss_or_take_profit(order_record)

    print(f"- 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    binance = BinanceAPI(symbol='ETHUSDT') 
    if response.get("action") == 'CANCEL_ORDER':
        binance.cancel_order(order_record['main_order']['orderId'])
        binance.cancel_order(order_record['stop_loss_order']['orderId'])
        binance.cancel_order(order_record['take_profit_order']['orderId'])

    elif response.get("action") == None:
        print("目前無需更動。")

    elif response.get("action") == 'MODIFY_STOP_LOSS_TAKE_PROFIT':
        stop_loss_order, take_profit_order = binance.create_stop_loss_take_profit_futures_order(
            quantity = order_record["main_order"]['positionAmt'],
            side = 'BUY' if order_record['take_profit_order']['stopPrice'] < order_record['stop_loss_order']['stopPrice'] else 'SELL',
            stop_loss_price=response.get('stop_loss_price'),
            take_profit_price=response.get('take_profit_price')
        )
        if stop_loss_order != None and take_profit_order != None:
            print("修改止損止盈價格成功！")
            with open("modify_reason.txt", 'w', encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                file.write(response.get("reason"))
            binance.cancel_order(order_record['stop_loss_order']['orderId'])
            binance.cancel_order(order_record['take_profit_order']['orderId'])
        else:
            print("修改止損止盈價格失敗！")
    
    elif response.get("action") == 'STOP_LOSS_TAKE_PROFIT':
        side = 'BUY' if order_record['take_profit_order']['stopPrice'] < order_record['stop_loss_order']['stopPrice'] else 'SELL'
        binance.close_order(side, quantity = order_record["main_order"]['positionAmt'])
        binance.cancel_order(order_record['stop_loss_order']['orderId'])
        binance.cancel_order(order_record['take_profit_order']['orderId'])
        with open("modify_reason.txt", 'w', encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(response.get("reason"))
    
    elif response.get("trading_opportunity") == 'Yes':
        handle_langchain_response_for_new_order(response)
    
    else:
        with open("no_order_reason.txt", 'w', encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            try:
                file.write(response.get("trading_reason"))
            except:
                pass
        print(response)
        print("目前無交易機會。")
    print()
    
def handle_langchain_response_for_new_order(response):
    with open(file_path, 'w', encoding='utf-8') as file:
        json.dump(response, file)
    with open("reason.txt", 'w', encoding='utf-8') as file:
        file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        file.write(response.get("trading_reason"))
    with open("modify_reason.txt", 'w', encoding='utf-8') as file:
        file.truncate(0)
    with open("no_order_reason.txt", 'w', encoding='utf-8') as file:
        file.truncate(0)
    binance = BinanceAPI(symbol='ETHUSDT')
    price = response.get("recommended_price")
    stop_loss_price = response.get("stop_loss_price")
    take_profit_price = response.get("take_profit_price")
    action = response.get("action")
    if action == "BUY":
        side = SIDE_BUY
    elif action == "SELL":
        side = SIDE_SELL

    if price and stop_loss_price and take_profit_price:
        print("根據Langchain Agent的建議，自動下訂單：")
        print(f"  行動: {action}")
        print(f"  價格: {price}")
        print(f"  止損價格: {stop_loss_price}")
        print(f"  止盈價格: {take_profit_price}")
        current_price = binance.get_price().get('price')
        quantity= round_up(20/float(current_price))
        order, stop_loss_order, take_profit_order = binance.create_futures_order(
            side=side,
            order_type=ORDER_TYPE_LIMIT,
            quantity=round(quantity,3),
            price=price,
            time_in_force=TIME_IN_FORCE_GTC,
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price
        )
        if float(current_price) >= float(price) and response.get("action")=="BUY":
            order["price"] = current_price
        elif float(current_price) <= float(price) and response.get("action")=="SELL":
            order["price"] = current_price
        if order != None:
            print("訂單下單成功！")
        else:
            print("餘額不足！")
    else:
        print("Langchain Agent回覆的價格或數量無效。")

def wait_until_next_interval(period_seconds=150):
    now = datetime.now()
    now_ts = now.timestamp()
    remainder = now_ts % period_seconds
    wait_time = period_seconds - remainder if remainder != 0 else 0
    return wait_time

if __name__ == '__main__':
    scheduler = BlockingScheduler()
    wait_until_next_interval()
    wait_time = wait_until_next_interval(150)
    print(f"等待 {wait_time:.2f} 秒直到下一個執行點位...")
    time.sleep(int(wait_time))
    print(f"開始執行...\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    scheduler.add_job(check_trading_status, 'interval', seconds = 150, next_run_time=datetime.now())
    scheduler.start()