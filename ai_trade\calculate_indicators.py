from binance_api import BinanceAPI
import talib
import numpy as np
from pprint import pprint   

class TechnicalIndicators:
    def __init__(self, symbol,time_frame):
        self.binance = BinanceAPI(symbol=symbol)
        self.timeframe = time_frame
        self.count = 0

    def get_kline_data(self):
        """獲取 K 線資訊"""
        if self.timeframe == '1分鐘':
            kline_info = self.binance.get_kline_1m()
            self.count = 30
        elif self.timeframe == '5分鐘':
            kline_info = self.binance.get_kline_5m()
            self.count = 120
        elif self.timeframe == '15分鐘':
            kline_info = self.binance.get_kline_15m()
            self.count = 120
        elif self.timeframe == '1小時':
            kline_info = self.binance.get_kline_1h()  # 獲取 1 小時 K 線資訊
            self.count = 50
        elif self.timeframe == '4小時':
            kline_info = self.binance.get_kline_4h()
            self.count = 20
        elif self.timeframe == '1天':
            kline_info = self.binance.get_kline_1d()
            self.count = 7
        closing_prices = np.array([float(kline[4]) for kline in kline_info])  # 提取收盤價格
        high_prices = np.array([float(kline[2]) for kline in kline_info])  # 提取最高價格
        low_prices = np.array([float(kline[3]) for kline in kline_info])  # 提取最低價格
        return closing_prices, high_prices, low_prices

    def calculate_moving_average_14(self, closing_prices, period=14):
        """計算簡單移動平均線 (SMA)"""
        return talib.SMA(closing_prices, timeperiod=period)
    
    def calculate_moving_average_7(self, closing_prices, period=7):
        """計算簡單移動平均線 (SMA)"""
        return talib.SMA(closing_prices, timeperiod=period)
    
    def calculate_atr(self, high_prices, low_prices, closing_prices, period=14):
        """計算真實波幅 (ATR)"""
        return talib.ATR(high_prices, low_prices, closing_prices, timeperiod=period)

    def calculate_rsi(self, closing_prices, period):
        """計算相對強弱指數 (RSI)"""
        return talib.RSI(closing_prices, timeperiod=period)

    def calculate_ema(self, closing_prices, period=14):
        """計算指數移動平均線 (EMA)"""
        return talib.EMA(closing_prices, timeperiod=period)

    def calculate_macd(self, closing_prices):
        """計算 MACD"""
        return talib.MACD(closing_prices, fastperiod=12, slowperiod=26, signalperiod=9)

    def calculate_kdj(self, high_prices, low_prices, closing_prices):
        """計算 KDJ"""
        return talib.STOCHF(high_prices, low_prices, closing_prices, fastk_period=14, fastd_period=3, fastd_matype=0)

    def get_kline_and_calculate_indicators(self):
        """獲取 K 線資訊並計算技術指標"""
        closing_prices, high_prices, low_prices = self.get_kline_data()

        atr = self.calculate_atr(high_prices, low_prices, closing_prices)  # 計算 ATR
        ma_7 = self.calculate_moving_average_7(closing_prices)  # 計算 MA
        ma_14 = self.calculate_moving_average_14(closing_prices)  # 計算 MA
        rsi_6 = self.calculate_rsi(closing_prices, period=6)  # 計算 6 期的 RSI
        rsi_12 = self.calculate_rsi(closing_prices, period=12)  # 計算 self.count 期的 RSI
        rsi_24 = self.calculate_rsi(closing_prices, period=24)  # 計算 self.count 期的 RSI
        ema = self.calculate_ema(closing_prices)  # 計算 EMA
        macd, macd_signal, macd_hist = self.calculate_macd(closing_prices)  # 計算 MACD
        
        # 計算 KDJ
        slowk, slowd = self.calculate_kdj(high_prices, low_prices, closing_prices)  # 計算 KDJ
        if self.timeframe == '1分鐘':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }
        elif self.timeframe == '5分鐘':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }
        elif self.timeframe == '15分鐘':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }
        elif self.timeframe == '1小時':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }
        elif self.timeframe == '4小時':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }
        elif self.timeframe == '1天':
            return {
                "atr": atr[-self.count:],  # 返回最新的 ATR 值
                "moving_average_7": ma_7[-self.count:],  # 返回最新的 MA 值
                "moving_average_14": ma_14[-self.count:],  # 返回最新的 MA 值
                "rsi_6": rsi_6[-self.count:],  # 返回最新的 RSI 值
                "rsi_12": rsi_12[-self.count:],  # 返回最新的 RSI 值
                "rsi_24": rsi_24[-self.count:],  # 返回最新的 RSI 值
                "ema": ema[-self.count:],  # 返回最新的 EMA 值
                "macd": macd[-self.count:],  # 返回最新的 MACD 值
                "macd_signal": macd_signal[-self.count:],  # 返回最新的 MACD 信號值
                "macd_hist": macd_hist[-self.count:],  # 返回最新的 MACD 直方圖值
                "kdj_k": slowk[-self.count:],  # 返回最新的 KDJ K 值
                "kdj_d": slowd[-self.count:],  # 返回最新的 KDJ D 值
            }

if __name__ == "__main__":
    symbol = 'ETHUSDT'  # 設定交易對
    indicators = TechnicalIndicators(symbol,'1分鐘')  # 初始化 TechnicalIndicators 類
    results = indicators.get_kline_and_calculate_indicators()

    # pprint("技術指標計算結果：")
    # pprint(f"真實波幅 (ATR): {results['atr']}")
    # pprint(f"移動平均線 (MA): {results['moving_average_14']}")
    # pprint(f"相對強弱指數 (RSI)(6): {results['rsi_6']}")
    # pprint(f"相對強弱指數 (RSI)(12): {results['rsi_12']}")
    # pprint(f"相對強弱指數 (RSI)(24): {results['rsi_24']}")
    pprint(f"指數移動平均線 (EMA): {results['ema']}")
    # pprint(f"MACD: {results['macd']}")
    # pprint(f"MACD 信號: {results['macd_signal']}")
    # pprint(f"KDJ K 值: {results['kdj_k']}")
    # pprint(f"KDJ D 值: {results['kdj_d']}")