from binance.client import Client
from binance.enums import *
from binance.exceptions import BinanceAPIException, BinanceOrderException
import os
from pprint import pprint
import json
from datetime import datetime, timedelta

class BinanceAPI:
    BASE_URL = 'https://api.binance.com/api/v3'

    def __init__(self, symbol):
        self.api_key = os.environ.get('binance_api')
        self.api_secret = os.environ.get('binance_key')
        self.client = Client(self.api_key, self.api_secret)  # 初始化客戶端
        self.symbol = symbol

    def get_price(self):
        price_info = self.client.futures_symbol_ticker(symbol=self.symbol)
        return price_info  # 返回價格資訊

    def get_kline_1m(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_1MINUTE)
        print(len(klines))
        return klines  # 返回最近的10根K線資訊

    def get_kline_5m(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_5MINUTE)
        return klines # 返回最近的10根K線資訊
        
    def get_kline_15m(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_15MINUTE)
        return klines # 返回最近的10根K線資訊
    
    def get_kline_1h(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_1HOUR)
        return klines  # 返回最近的10根K線資訊

    def get_kline_4h(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_4HOUR)
        return klines
    
    def get_kline_1d(self):
        klines = self.client.futures_klines(symbol=self.symbol, interval=KLINE_INTERVAL_1DAY)
        return klines

    def get_order_book(self):
        depth = self.client.get_order_book(symbol=self.symbol)
        return {
            "bids": depth['bids'][:10],
            "asks": depth['asks'][:10]
        }  # 返回買單和賣單資訊

    def create_side_buy_order(self, quantity, price):
        """創建交易訂單"""
        try:
            order = self.client.order_limit_buy(
                symbol=self.symbol,
                quantity=quantity,
                price=price)
            return order
        except BinanceAPIException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"API Exception: {e}")
        except BinanceOrderException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"Order Exception: {e}")

    def create_futures_order(self, side, order_type, quantity, price, time_in_force, stop_loss_price, take_profit_price):
        """創建合約交易訂單並設置止損和止盈"""
        try:
            # 創建主訂單
            order = self.client.futures_create_order(
                symbol=self.symbol,
                side=side,
                type=order_type,
                quantity=quantity,
                price=price,
                timeInForce=time_in_force
            )

            # 設置止損訂單
            stop_loss_order = self.client.futures_create_order(
                symbol=self.symbol,
                side="SELL" if side == "BUY" else "BUY",
                type="STOP_MARKET",
                stopPrice=stop_loss_price,  # 設置止損價格
                quantity=quantity,
                reduceOnly=True
            )

            # 設置止盈訂單
            take_profit_order = self.client.futures_create_order(
                symbol=self.symbol,
                side="SELL" if side == "BUY" else "BUY",
                type="TAKE_PROFIT_MARKET",
                stopPrice=take_profit_price,  # 設置止盈價格
                quantity=quantity,
                reduceOnly=True
            )
            # print(order)
            order_record = {
                'main_order':order,
                'stop_loss_order':stop_loss_order,
                'take_profit_order':take_profit_order
            }
            # with open('order_record.json','w',encoding='utf-8') as file:
            #     json.dump(order_record,file,indent=3)
        except BinanceAPIException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"API Exception: {e}")
            order = None
            stop_loss_order = None
            take_profit_order = None
        except BinanceOrderException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"Order Exception: {e}")
            order = None
            stop_loss_order = None
            take_profit_order = None
        finally:
            return order, stop_loss_order, take_profit_order

    def create_stop_loss_take_profit_futures_order(self, quantity, side, stop_loss_price, take_profit_price):
        try:
            quantity_abs = abs(float(quantity))
            # 設置止損訂單
            stop_loss_order = self.client.futures_create_order(
                symbol=self.symbol,
                side=side,
                type="STOP_MARKET",
                stopPrice=stop_loss_price,  # 設置止損價格
                quantity=quantity_abs,
                reduceOnly=True  # 表示這是一個平倉訂單
            )
            # 設置止盈訂單
            take_profit_order = self.client.futures_create_order(
                symbol=self.symbol,
                side=side,
                type="TAKE_PROFIT_MARKET",
                stopPrice=take_profit_price,  # 設置止盈價格
                quantity=quantity_abs,
                reduceOnly=True  # 表示這是一個平倉訂單
            )
            return stop_loss_order, take_profit_order
        except BinanceAPIException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            error_message = str(e)
            print(f"API Exception: {error_message}")
            
            if "APIError(code=-2021): Order would immediately trigger." in error_message:
                print("檢測到訂單會立即觸發，正在關閉訂單...")
                self.close_order(side, quantity)
            return None, None

        except BinanceOrderException as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"Order Exception: {e}")
            return None, None
        
    def get_account_info(self):
        return self.client.get_asset_balance(asset='BTC')

    def get_exchange_info(self):
        exchange_info = self.client.futures_exchange_info()
        for symbol_info in exchange_info['symbols']:
            if symbol_info['symbol'] == self.symbol:
                pprint(symbol_info)
                step_size = symbol_info['filters'][1]['stepSize']
                tick_size = symbol_info['filters'][0]['tickSize']
        return {"step_size": step_size, "tick_size": tick_size, "minPrice": symbol_info['filters'][0]['minPrice']}

    def change_futures_leverage(self, leverage=20):
        self.client.futures_change_leverage(symbol=self.symbol, leverage=leverage)

    def get_trading_status(self):
        orders = self.client.futures_get_open_orders(symbol = self.symbol)
        return orders

    def get_open_futures_orders(self):
        try:
            # 如果指定 symbol，查詢特定交易對的訂單；否則查詢所有交易對
            open_orders = self.client.futures_position_information(symbol = self.symbol)
            return open_orders
        except Exception as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print(f"查詢失敗: {e}")
            return []
        
    def cancel_order(self, order_id):
        # 取消特定訂單
        try:
            cancel_response = self.client.futures_cancel_order(symbol=self.symbol, orderId=order_id)
            print("取消訂單成功!")
            return cancel_response
        except Exception as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print("取消訂單失敗:", e)
            return None
    
    def close_order(self, side, quantity):
        try:
            quantity_abs = abs(float(quantity))
            close_response = self.client.futures_create_order(
                symbol=self.symbol,
                side=side,
                type="MARKET",
                quantity=quantity_abs,
                reduceOnly=True  # 表示這是一個平倉訂單
            )
            print("平倉訂單成功:", close_response)
            return close_response
        except Exception as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"平倉訂單失敗: {e}\n")
                file.write(f"side: {side}\n")
                file.write(f"quantity: {quantity}\n")

            print("平倉訂單失敗:", e)
            return None

    def get_futures_order_history(self):
        orders = []
        trades = self.client.futures_account_trades(symbol=self.symbol, limit=16)  # 只獲取已成交交易
        now = datetime.now()  # 當前時間
        time_threshold = now - timedelta(hours=20)  # 設定時間範圍（20 小時內）
        for trade in trades:
            trade_time = datetime.fromtimestamp(trade['time'] / 1000)  # 轉換成交時間（毫秒轉秒）
            if trade_time >= time_threshold:
                orders.append(trade)
        # pprint(orders)
        return orders



# 使用範例
if __name__ == "__main__":
    file_path = 'order_record.json'
    binance = BinanceAPI(symbol='ETHUSDT')
    # print("    binance.get_price()輸出結果：")
    # print(binance.get_price())
    # print()
    # print("    binance.get_kline()輸出結果：")
    # print(binance.get_kline())
    # print()
    # print("    binance.get_order_book()輸出結果：")
    # print(binance.get_order_book())
    # print()
    # order, stop_loss_order, take_profit_order = binance.create_futures_order(SIDE_BUY,                 
    #             order_type=ORDER_TYPE_LIMIT,
    #             quantity=0.001,
    #             price=98776,
    #             time_in_force=TIME_IN_FORCE_GTC,  # Good Till Cancelled
    #             stop_loss_price=99500,
    #             take_profit_price=79020)
    # if order != None:
    #     # 儲存訂單資訊到 JSON 檔案
    #     order_info = {
    #         "main_order": order,
    #         "stop_loss_order": stop_loss_order,
    #         "take_profit_order": take_profit_order
    #     }
    #     with open(file_path, 'w', encoding='utf-8') as file:
    #         json.dump(order_info, file, indent=3)

    #     print("下訂單結果：", order)
    #     print("止損訂單結果：", stop_loss_order)
    #     print("止盈訂單結果：", take_profit_order)
    # print("    binance.get_trading_status()輸出結果：")

    # with open(file_path, 'r', encoding='utf-8') as file:
    #     try:
    #         data = json.load(file)
    #     except json.JSONDecodeError as e:
    #         print(f"讀取訂單記錄時出錯: {e}")
    #         data =  None
    # pprint(binance.get_open_futures_orders())
    # binance.create_stop_loss_take_profit_futures_order(            
    #         side='SELL',
    #         stop_loss_price='3130',
    #         take_profit_price='3740',
    #         quantity=0.03)
    pprint(binance.get_futures_order_history())

